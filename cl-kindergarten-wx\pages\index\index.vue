<template>
	<view class="container">
		<!-- 管理员已登录时显示管理功能 -->
		<view v-if="isAdminLoggedIn" class="admin-container">
			<!-- 主要内容区域 -->
			<view class="main-content">
				<!-- 管理功能标题 -->
				<view class="section-title">管理功能</view>

				<!-- 功能菜单区域 -->
				<view class="menu-grid">
					<view class="menu-item" @click="goToStudentAttendanceManagement">
						<view class="menu-icon">📝</view>
						<text class="menu-text">学生考勤管理</text>
					</view>
					<view class="menu-item" @click="goToTeacherAttendanceManagement">
						<view class="menu-icon">👩‍🏫</view>
						<text class="menu-text">教师考勤管理</text>
					</view>
					<view class="menu-item" @click="goToClassManagement">
						<view class="menu-icon">🏫</view>
						<text class="menu-text">班级管理</text>
					</view>
					<view class="menu-item" @click="goToUserManagement">
						<view class="menu-icon">👥</view>
						<text class="menu-text">用户管理</text>
					</view>
					<view class="menu-item" @click="goToCourseManagement">
						<view class="menu-icon">📚</view>
						<text class="menu-text">课程管理</text>
					</view>
					<view class="menu-item" @click="goToFeeManagement">
						<view class="menu-icon">💰</view>
						<text class="menu-text">园费管理</text>
					</view>
					<view class="menu-item" @click="goToStudentManagement">
						<view class="menu-icon">👶</view>
						<text class="menu-text">学生管理</text>
					</view>
					<view class="menu-item" @click="goToTeacherManagement">
						<view class="menu-icon">👨‍🏫</view>
						<text class="menu-text">教师管理</text>
					</view>
					<view class="menu-item" @click="goToInventoryManagement">
						<view class="menu-icon">📦</view>
						<text class="menu-text">库存管理</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 考勤类型选择弹窗 -->
		<view v-if="showAttendanceModal" class="attendance-modal-overlay" @click="closeAttendanceModal">
			<view class="attendance-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择考勤类型</text>
					<view class="modal-close" @click="closeAttendanceModal">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="modal-content">
					<view class="attendance-option" @click="selectAttendanceType('student')">
						<view class="option-icon">👨‍🎓</view>
						<view class="option-info">
							<text class="option-title">学生考勤</text>
							<text class="option-desc">管理学生日常考勤记录</text>
						</view>
						<view class="option-arrow">
							<text class="arrow-icon">→</text>
						</view>
					</view>
					<view class="attendance-option" @click="selectAttendanceType('custody')">
						<view class="option-icon">📚</view>
						<view class="option-info">
							<text class="option-title">托管考勤</text>
							<text class="option-desc">管理课程托管考勤记录</text>
						</view>
						<view class="option-arrow">
							<text class="arrow-icon">→</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, clearStorageSync, useRouter, getStorageSync} from '@/utils/utils.js'

export default {
	data() {
		return {
			isAdminLoggedIn: false,
			showAttendanceModal: false, // 考勤类型选择弹窗
			adminUser: {
				name: '',
				phone: '',
				avatar: '',
				company_name: '',
				registerDate: '',
				login_code: '',
				auth: 0
			}
		};
	},
	onLoad() {
		this.checkAdminLogin();
	},
	onShow() {
		// 每次显示页面时都检查登录状态
		this.checkAdminLogin();
	},
	methods: {
		// 检查管理员登录状态
		checkAdminLogin() {
			const adminToken = getStorageSync('admin_token');
			console.log('首页检查admin_token:', adminToken);
			this.isAdminLoggedIn = !!adminToken;

			if (adminToken) {
				console.log('有admin_token，设置登录状态');
				// 加载用户信息
				this.loadAdminUser();
			} else {
				console.log('没有admin_token，跳转到登录页面');
				// 如果没有admin_token，跳转到登录页面
				useRouter('/pages/public/login', {}, 'navigateTo');
			}
		},

		// 加载管理员用户信息
		loadAdminUser() {
			// 先从storage中获取缓存的用户信息
			const cachedUser = getStorageSync('admin_user');
			if (cachedUser) {
				this.adminUser = cachedUser;
			}
		},

		// 跳转到"我的"页面
		goToMyPage() {
			uni.switchTab({
				url: '/pages/my/index'
			});
		},

		// 学生考勤管理 - 显示选择弹窗
		goToStudentAttendanceManagement() {
			this.showAttendanceTypeModal();
		},

		// 显示考勤类型选择弹窗
		showAttendanceTypeModal() {
			this.showAttendanceModal = true;
		},

		// 关闭考勤类型选择弹窗
		closeAttendanceModal() {
			this.showAttendanceModal = false;
		},

		// 选择考勤类型
		selectAttendanceType(type) {
			this.closeAttendanceModal();
			if (type === 'student') {
				this.goToStudentAttendance();
			} else if (type === 'custody') {
				this.goToCustodyAttendance();
			}
		},

		// 跳转到学生考勤页面
		goToStudentAttendance() {
			useRouter('/pages/admin/attendance/student/index', {}, 'navigateTo');
		},

		// 跳转到托管考勤页面
		goToCustodyAttendance() {
			useRouter('/pages/admin/attendance/custody/index', {}, 'navigateTo');
		},

		// 教师考勤管理
		goToTeacherAttendanceManagement() {
			useRouter('/pages/admin/attendance/teacher/index', {}, 'navigateTo');
		},

		// 班级管理
		goToClassManagement() {
			useRouter('/pages/admin/class/index', {}, 'navigateTo');
		},

		// 用户管理
		goToUserManagement() {
			useRouter('/pages/admin/user/index', {}, 'navigateTo');
		},

		// 课程管理
		goToCourseManagement() {
			useRouter('/pages/admin/course/index', {}, 'navigateTo');
		},

		// 园费管理
		goToFeeManagement() {
			useRouter('/pages/admin/fee/index', {}, 'navigateTo');
		},

		// 学生管理
		goToStudentManagement() {
			useRouter('/pages/admin/student/index', {}, 'navigateTo');
		},

		// 教师管理
		goToTeacherManagement() {
			useRouter('/pages/admin/teacher/index', {}, 'navigateTo');
		},

		// 库存管理
		goToInventoryManagement() {
			useRouter('/pages/admin/inventory/index', {}, 'navigateTo');
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除管理员token
						clearStorageSync('admin_token');
						toast('已退出登录');
						// 跳转到我的页面
						useRouter('/pages/my/index', {}, 'switchTab');
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.admin-container {
	min-height: 100vh;
	background: #f8f9fa;
}

/* 头部区域 */
.header {
	background: #ffffff;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.admin-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	background: #e3f2fd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.admin-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.admin-position {
	font-size: 24rpx;
	color: #666;
}

.header-actions {
	.header-btn {
		background: #ff4757;
		color: white;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 24rpx;
		font-weight: 500;
	}
}

/* 主要内容区域 */
.main-content {
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #4CAF50;
}

/* 功能菜单网格 */
.menu-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.menu-item {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	border: 1rpx solid #f0f0f0;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
	}
}

.menu-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
	display: block;
}

.menu-text {
	display: block;
	font-size: 24rpx;
	color: #333333;
	font-weight: 500;
	line-height: 1.4;
}

/* 响应式适配 */
@media screen and (max-width: 480px) {
	.header {
		padding: 30rpx;
	}

	.main-content {
		padding: 30rpx;
	}

	.menu-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 24rpx;
	}

	.menu-item {
		padding: 40rpx 20rpx;
	}

	.menu-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
	}

	.menu-text {
		font-size: 28rpx;
	}
}

/* 考勤类型选择弹窗 */
.attendance-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}

.attendance-modal {
	background: #ffffff;
	border-radius: 24rpx;
	margin: 60rpx;
	max-width: 600rpx;
	width: 100%;
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: #e0e0e0;
		transform: scale(0.9);
	}
}

.close-icon {
	font-size: 40rpx;
	color: #999999;
	line-height: 1;
}

.modal-content {
	padding: 20rpx 40rpx 40rpx;
}

.attendance-option {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	margin-bottom: 16rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 20rpx;
	transition: all 0.3s ease;

	&:last-child {
		margin-bottom: 0;
	}

	&:active {
		transform: scale(0.98);
		background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	}
}

.option-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	margin-right: 24rpx;
}

.option-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.option-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.option-desc {
	font-size: 26rpx;
	color: #666666;
}

.option-arrow {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow-icon {
	font-size: 28rpx;
	color: #667eea;
	font-weight: 600;
}
</style>