### 学生签到确认组合接口测试

### 1. 正常签到确认测试
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1001,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1",
  "remark": "手动签到确认"
}

### 2. 参数验证测试 - 缺少学生ID
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1"
}

### 3. 参数验证测试 - 缺少考勤日期
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1001,
  "attendanceStatus": "1"
}

### 4. 日期格式错误测试
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1001,
  "attendanceDate": "2025/08/04",
  "attendanceStatus": "1"
}

### 5. 不存在的学生ID测试
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 99999,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1"
}

### 6. 不同考勤状态测试 - 迟到
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1001,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "2",
  "remark": "迟到签到"
}

### 7. 重复签到测试（同一学生同一天多次签到）
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1001,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1",
  "remark": "第二次签到"
}

### 8. 批量测试 - 多个学生
POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1002,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1",
  "remark": "学生1002签到确认"
}

###

POST http://localhost:8080/business/student-attendance/checkinAndConfirm
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentId": 1003,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1",
  "remark": "学生1003签到确认"
}

### 9. 验证数据库记录
GET http://localhost:8080/business/student-attendance/overview?attendanceDate=2025-08-04
Authorization: Bearer {{token}}

### 10. 验证确认状态
GET http://localhost:8080/business/student-attendance/list?attendanceDate=2025-08-04&studentId=1001
Authorization: Bearer {{token}}

### 预期响应格式
# 成功响应:
# {
#   "code": 200,
#   "msg": "签到确认成功",
#   "data": 12345
# }

# 失败响应:
# {
#   "code": 500,
#   "msg": "签到确认失败：具体错误信息"
# }

### 测试检查点
# 1. 响应状态码是否正确
# 2. 返回的考勤记录ID是否有效
# 3. 数据库中是否正确创建了考勤记录
# 4. 考勤记录的确认状态是否为1
# 5. 签到时间是否正确设置
# 6. 事务回滚是否正常工作
