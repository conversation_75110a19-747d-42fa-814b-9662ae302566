package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgStudentAttendanceMapper;
import com.cl.project.business.mapper.KgDingtalkAttendanceMapper;
import com.cl.project.business.service.IKgStudentService;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.domain.dto.StudentAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchStudentCheckinDto;
import com.cl.project.business.domain.dto.BatchConfirmAttendanceDto;
import com.cl.project.business.service.IKgStudentAttendanceService;

/**
 * 学生考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgStudentAttendanceServiceImpl implements IKgStudentAttendanceService 
{
    @Autowired
    private KgStudentAttendanceMapper kgStudentAttendanceMapper;
    
    @Autowired
    private KgDingtalkAttendanceMapper kgDingtalkAttendanceMapper;

    @Autowired
    private IKgStudentService kgStudentService;

    /**
     * 查询学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 学生考勤记录
     */
    @Override
    public KgStudentAttendance selectKgStudentAttendanceById(Long attendanceId)
    {
        return kgStudentAttendanceMapper.selectKgStudentAttendanceById(attendanceId);
    }

    /**
     * 查询学生考勤记录列表
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 学生考勤记录
     */
    @Override
    public List<KgStudentAttendance> selectKgStudentAttendanceList(KgStudentAttendance kgStudentAttendance)
    {
        return kgStudentAttendanceMapper.selectKgStudentAttendanceList(kgStudentAttendance);
    }

    /**
     * 新增学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int insertKgStudentAttendance(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setCreateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.insertKgStudentAttendance(kgStudentAttendance);
    }

    /**
     * 修改学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int updateKgStudentAttendance(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.updateKgStudentAttendance(kgStudentAttendance);
    }

    /**
     * 批量删除学生考勤记录
     * 
     * @param attendanceIds 需要删除的学生考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentAttendanceByIds(Long[] attendanceIds)
    {
        return kgStudentAttendanceMapper.deleteKgStudentAttendanceByIds(attendanceIds);
    }

    /**
     * 删除学生考勤记录信息
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentAttendanceById(Long attendanceId)
    {
        return kgStudentAttendanceMapper.deleteKgStudentAttendanceById(attendanceId);
    }

    /**
     * 查询学生考勤概览列表
     * 展示所有学生及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期，为null时查询当日
     * @param studentName 学生姓名
     * @param classId 班级ID
     * @param attendanceStatus 考勤状态
     * @param dataSource 数据来源
     * @return 学生考勤概览集合
     */
    @Override
    public List<StudentAttendanceOverviewDto> selectStudentAttendanceOverview(Date attendanceDate, String studentName, Long classId, String attendanceStatus, String dataSource)
    {
        // 如果未指定日期，默认查询当日
        if (attendanceDate == null) {
            attendanceDate = DateUtils.getNowDate();
        }
        List<StudentAttendanceOverviewDto> overviewList = kgStudentAttendanceMapper.selectStudentAttendanceOverview(attendanceDate, studentName, classId, attendanceStatus, dataSource);
        if (overviewList == null || overviewList.isEmpty()) {
            return overviewList;
        }
        
        // 查询所有学生当天的钉钉打卡记录和手动签到记录
        for (StudentAttendanceOverviewDto dto : overviewList) {
            Long studentId = dto.getStudentId();
            Date date = dto.getAttendanceDate();
            if (studentId != null && date != null) {
                Date dateFrom = com.cl.common.utils.DateUtils.getDayStart(date);
                Date dateTo = com.cl.common.utils.DateUtils.getDayEnd(date);
                
                // 钉钉打卡明细
                List<com.cl.project.business.domain.KgDingtalkAttendance> records = kgDingtalkAttendanceMapper.selectByStudentIdAndDate(studentId, dateFrom, dateTo);
                dto.setDingtalkRecords(records);
                
                // 根据钉钉打卡记录设置考勤状态：有上班和下班记录为出勤(1)，否则为缺勤(3)
                if (records != null && !records.isEmpty()) {
                    boolean hasOnDuty = records.stream()
                            .anyMatch(r -> "OnDuty".equals(r.getCheckType()));
                    boolean hasOffDuty = records.stream()
                            .anyMatch(r -> "OffDuty".equals(r.getCheckType()));
                    
                    // 计算在校时长
                    java.util.Optional<com.cl.project.business.domain.KgDingtalkAttendance> onDutyOpt = records.stream()
                            .filter(r -> "OnDuty".equals(r.getCheckType()))
                            .min(java.util.Comparator.comparing(com.cl.project.business.domain.KgDingtalkAttendance::getCheckTime));
                    java.util.Optional<com.cl.project.business.domain.KgDingtalkAttendance> offDutyOpt = records.stream()
                            .filter(r -> "OffDuty".equals(r.getCheckType()))
                            .max(java.util.Comparator.comparing(com.cl.project.business.domain.KgDingtalkAttendance::getCheckTime));
                    
                    if (onDutyOpt.isPresent() && offDutyOpt.isPresent()) {
                        Date onDuty = onDutyOpt.get().getCheckTime();
                        Date offDuty = offDutyOpt.get().getCheckTime();
                        long durationMillis = offDuty.getTime() - onDuty.getTime();
                        if (durationMillis > 0) {
                            java.math.BigDecimal hours = new java.math.BigDecimal(durationMillis / (1000.0 * 60 * 60));
                            dto.setSchoolHours(hours.setScale(1, java.math.RoundingMode.HALF_UP));
                        } else {
                            dto.setSchoolHours(java.math.BigDecimal.ZERO);
                        }
                    } else {
                        dto.setSchoolHours(java.math.BigDecimal.ZERO);
                    }
                    
                    if (hasOnDuty && hasOffDuty) {
                        dto.setAttendanceStatus("1"); // 出勤
                    } else {
                        dto.setAttendanceStatus("3"); // 缺勤
                    }
                } else {
                    dto.setAttendanceStatus("3"); // 无打卡记录，默认缺勤
                    dto.setSchoolHours(java.math.BigDecimal.ZERO);
                }
                
                // 手动签到明细
                List<KgStudentAttendance> manualList = kgStudentAttendanceMapper.selectManualByStudentAndDate(studentId, dateFrom, dateTo);
                dto.setManualRecords(manualList);
                
                // 设置概览行的isConfirmed：全部手动签到都已确认为1，否则为0
                if (manualList != null && !manualList.isEmpty() && manualList.stream().allMatch(m -> m.getIsConfirmed() != null && m.getIsConfirmed() == 1)) {
                    dto.setIsConfirmed(1L);
                } else {
                    dto.setIsConfirmed(0L);
                }
                
                // 设置checkInTime为manualList中最新签到时间
                if (manualList != null && !manualList.isEmpty()) {
                    Date latestCheckInTime = manualList.stream()
                            .map(KgStudentAttendance::getCheckInTime)
                            .filter(java.util.Objects::nonNull)
                            .max(Date::compareTo)
                            .orElse(null);
                    dto.setCheckInTime(latestCheckInTime);
                }
            } else {
                dto.setDingtalkRecords(java.util.Collections.emptyList());
                dto.setManualRecords(java.util.Collections.emptyList());
            }
        }
        
        // 在Service层处理筛选条件，与教师考勤保持一致
        return overviewList.stream()
                .filter(dto -> {
                    // 学生姓名筛选
                    if (studentName != null && !studentName.trim().isEmpty()) {
                        if (dto.getStudentName() == null || !dto.getStudentName().contains(studentName)) {
                            return false;
                        }
                    }
                    
                    // 班级筛选
                    if (classId != null) {
                        if (dto.getClassId() == null || !dto.getClassId().equals(classId)) {
                            return false;
                        }
                    }
                    
                    // 考勤状态筛选
                    if (attendanceStatus != null && !attendanceStatus.trim().isEmpty()) {
                        if (dto.getAttendanceStatus() == null || !dto.getAttendanceStatus().equals(attendanceStatus)) {
                            return false;
                        }
                    }
                    
                    // 数据来源筛选
                    if (dataSource != null && !dataSource.trim().isEmpty()) {
                        if ("manual".equals(dataSource)) {
                            // 只显示有手动签到记录的学生
                            return dto.getManualRecords() != null && !dto.getManualRecords().isEmpty();
                        } else if ("dingtalk".equals(dataSource)) {
                            // 只显示有钉钉打卡记录的学生
                            return dto.getDingtalkRecords() != null && !dto.getDingtalkRecords().isEmpty();
                        }
                    }
                    
                    return true;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 学生签到
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int studentCheckin(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setCheckInTime(DateUtils.getNowDate());
        kgStudentAttendance.setAttendanceStatus("1"); // 1-出勤
        kgStudentAttendance.setCreateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.insertKgStudentAttendance(kgStudentAttendance);
    }

    /**
     * 批量学生签到
     * 支持一天多次签到，每次都创建新记录
     * 
     * @param batchDto 批量签到数据
     * @return 成功处理的记录数
     */
    @Override
    @Transactional
    public int batchStudentCheckin(BatchStudentCheckinDto batchDto)
    {
        int successCount = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        
        try {
            Date attendanceDate = sdf.parse(batchDto.getAttendanceDate());
            String currentUser = SecurityUtils.getUsername();
            Date currentTime = DateUtils.getNowDate();
            
            for (Long studentId : batchDto.getStudentIds()) {
                // 每次签到都创建新记录，支持一天多次签到
                KgStudentAttendance newRecord = new KgStudentAttendance();
                newRecord.setStudentId(studentId);
                newRecord.setAttendanceDate(attendanceDate);
                newRecord.setAttendanceStatus(batchDto.getAttendanceStatus());

                // 查询学生班级ID并赋值
                KgStudent student = kgStudentService.selectKgStudentById(studentId);
                if (student != null && student.getClassId() != null) {
                    newRecord.setClassId(student.getClassId());
                } else {
                    newRecord.setClassId(null); // 或根据业务需要处理
                }
                
                // 设置签到时间和方式
                if ("1".equals(batchDto.getAttendanceStatus())) { // 签到
                    newRecord.setCheckInTime(currentTime);
                    newRecord.setCheckInMethod("manual");
                } else if ("3".equals(batchDto.getAttendanceStatus()) || "4".equals(batchDto.getAttendanceStatus())) {
                    // 缺勤或请假不需要设置签到时间
                    newRecord.setCheckInTime(null);
                    newRecord.setCheckInMethod(null);
                }
                
                newRecord.setRemark(batchDto.getRemark());
                newRecord.setCreateBy(currentUser);
                newRecord.setCreateTime(currentTime);
                
                if (kgStudentAttendanceMapper.insertKgStudentAttendance(newRecord) > 0) {
                    successCount++;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("批量签到操作失败: " + e.getMessage(), e);
        }
        
        return successCount;
    }

    /**
     * 单个确认学生考勤
     * 
     * @param attendanceId 考勤记录ID
     * @return 结果
     */
    @Override
    public int confirmStudentAttendance(Long attendanceId)
    {
        KgStudentAttendance attendance = new KgStudentAttendance();
        attendance.setAttendanceId(attendanceId);
        attendance.setIsConfirmed(1); // 1-已确认
        attendance.setUpdateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.updateKgStudentAttendance(attendance);
    }

    /**
     * 批量确认学生考勤
     * 
     * @param batchDto 批量确认数据
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchConfirmAttendance(BatchConfirmAttendanceDto batchDto)
    {
        if (batchDto == null || batchDto.getAttendanceIds() == null || batchDto.getAttendanceIds().isEmpty()) {
            return 0;
        }
        // 直接调用Mapper批量更新
        return kgStudentAttendanceMapper.batchConfirmAttendance(batchDto.getAttendanceIds(), batchDto.getConfirmedBy());
    }

    /**
     * 缺勤登记
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int registerAbsence(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setAttendanceStatus("3"); // 3-缺勤
        kgStudentAttendance.setCreateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.insertKgStudentAttendance(kgStudentAttendance);
    }
}
