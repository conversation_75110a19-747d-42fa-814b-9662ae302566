# 学生考勤签到确认组合接口测试

## 接口信息
- **接口路径**: `POST /business/student-attendance/checkinAndConfirm`
- **接口描述**: 学生签到并自动确认
- **权限要求**: `kg:attendance:student:checkin`

## 测试用例

### 1. 正常签到确认测试

#### 请求示例
```bash
curl -X POST "http://localhost:8080/business/student-attendance/checkinAndConfirm" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "studentId": 1001,
    "attendanceDate": "2025-08-04",
    "attendanceStatus": "1",
    "remark": "手动签到确认"
  }'
```

#### 预期响应
```json
{
  "code": 200,
  "msg": "签到确认成功",
  "data": 12345
}
```

### 2. 参数验证测试

#### 缺少学生ID
```json
{
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1"
}
```

**预期响应**: 400 Bad Request，提示"学生ID不能为空"

#### 缺少考勤日期
```json
{
  "studentId": 1001,
  "attendanceStatus": "1"
}
```

**预期响应**: 400 Bad Request，提示"考勤日期不能为空"

#### 日期格式错误
```json
{
  "studentId": 1001,
  "attendanceDate": "2025/08/04",
  "attendanceStatus": "1"
}
```

**预期响应**: 500 Internal Server Error，提示"日期格式错误"

### 3. 业务逻辑测试

#### 不存在的学生ID
```json
{
  "studentId": 99999,
  "attendanceDate": "2025-08-04",
  "attendanceStatus": "1"
}
```

**预期响应**: 根据数据库约束，可能返回外键约束错误

#### 重复签到测试
对同一学生同一天多次调用接口，验证是否允许多次签到。

### 4. 事务测试

#### 模拟确认失败场景
通过修改数据库状态或权限，模拟确认步骤失败，验证事务是否正确回滚。

## 数据库验证

### 验证考勤记录创建
```sql
SELECT * FROM kg_student_attendance 
WHERE student_id = 1001 
  AND attendance_date = '2025-08-04'
ORDER BY create_time DESC;
```

### 验证确认状态
```sql
SELECT attendance_id, student_id, attendance_date, 
       check_in_time, attendance_status, is_confirmed,
       create_time, update_time
FROM kg_student_attendance 
WHERE attendance_id = 12345;
```

**预期结果**:
- `is_confirmed` = 1
- `check_in_time` 不为空
- `attendance_status` = '1'

## 性能测试

### 并发测试
使用JMeter或类似工具，模拟多个用户同时为不同学生签到确认，验证接口性能和数据一致性。

### 压力测试
测试接口在高并发情况下的响应时间和成功率。

## 集成测试

### 小程序端集成测试
1. 在小程序端调用新接口
2. 验证返回数据格式
3. 验证页面数据刷新
4. 验证用户提示信息

### Web端兼容性测试
确保新接口不影响现有web端功能。

## 错误处理测试

### 网络异常
模拟网络中断、超时等情况，验证错误处理机制。

### 数据库异常
模拟数据库连接失败、表锁定等情况，验证事务回滚。

## 安全测试

### 权限验证
- 无token访问
- 过期token访问  
- 无权限用户访问

### 参数安全
- SQL注入测试
- XSS攻击测试
- 参数篡改测试

## 回归测试

### 现有功能验证
确保新接口不影响以下现有功能：
- 单独签到接口
- 单独确认接口
- 批量签到接口
- 批量确认接口
- 考勤数据查询

## 测试检查清单

- [ ] 正常流程测试通过
- [ ] 参数验证测试通过
- [ ] 业务逻辑测试通过
- [ ] 事务回滚测试通过
- [ ] 数据库数据验证通过
- [ ] 性能测试通过
- [ ] 集成测试通过
- [ ] 错误处理测试通过
- [ ] 安全测试通过
- [ ] 回归测试通过

## 测试环境要求

- Java 8+
- Spring Boot 2.x
- MySQL 5.7+
- Redis (如果使用缓存)
- 测试数据库环境
- 有效的测试用户和权限配置
