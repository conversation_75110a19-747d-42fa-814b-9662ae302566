export default {
    props: {
        // 是否显示组件
        show: {
            type: <PERSON><PERSON><PERSON>,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.show ?? true
                } catch (e) {
                    return true
                }
            }
        },
        // 颜色
        color: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.color ?? '#909399'
                } catch (e) {
                    return '#909399'
                }
            }
        },
        // 提示文字颜色
        textColor: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.textColor ?? '#909399'
                } catch (e) {
                    return '#909399'
                }
            }
        },
        // 文字和图标是否垂直排列
        vertical: {
            type: <PERSON><PERSON><PERSON>,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.vertical ?? false
                } catch (e) {
                    return false
                }
            }
        },
        // 模式选择，circle-圆形，spinner-花朵形，semicircle-半圆形
        mode: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.mode ?? 'spinner'
                } catch (e) {
                    return 'spinner'
                }
            }
        },
        // 图标大小，单位默认px
        size: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.size ?? 24
                } catch (e) {
                    return 24
                }
            }
        },
        // 文字大小
        textSize: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.textSize ?? 15
                } catch (e) {
                    return 15
                }
            }
        },
        // 文字内容
        text: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.text ?? ''
                } catch (e) {
                    return ''
                }
            }
        },
        // 动画模式
        timingFunction: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.timingFunction ?? 'ease-in-out'
                } catch (e) {
                    return 'ease-in-out'
                }
            }
        },
        // 动画执行周期时间
        duration: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.duration ?? 1200
                } catch (e) {
                    return 1200
                }
            }
        },
        // mode=circle时的暗边颜色
        inactiveColor: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.inactiveColor ?? ''
                } catch (e) {
                    return ''
                }
            }
        },
        // 定义需要用到的外部样式
        customStyle: {
            type: [Object, String],
            default: () => {
                try {
                    return uni.$u?.props?.loadingIcon?.customStyle ?? {}
                } catch (e) {
                    return {}
                }
            }
        }
    }
}
