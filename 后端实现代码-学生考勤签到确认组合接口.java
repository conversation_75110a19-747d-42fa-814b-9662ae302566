// ========== DTO类 ==========
// 文件路径: cl-kindergarten/src/main/java/com/cl/project/business/domain/dto/StudentCheckinAndConfirmDto.java

package com.cl.project.business.domain.dto;

import javax.validation.constraints.NotNull;

/**
 * 学生签到并确认DTO
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class StudentCheckinAndConfirmDto {
    
    /** 学生ID */
    @NotNull(message = "学生ID不能为空")
    private Long studentId;
    
    /** 考勤日期 */
    @NotNull(message = "考勤日期不能为空")
    private String attendanceDate;
    
    /** 考勤状态 (1出勤 2迟到 3缺勤 4请假 5早退 6病假 8休假) */
    private String attendanceStatus = "1"; // 默认出勤
    
    /** 备注 */
    private String remark;

    // Getter and Setter methods
    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

// ========== Controller层新增方法 ==========
// 在文件: cl-kindergarten/src/main/java/com/cl/project/business/controller/KgStudentAttendanceController.java
// 在类中添加以下方法:

/**
 * 学生签到并确认
 */
@SaCheckPermission("kg:attendance:student:checkin")
@Log(title = "学生签到确认", businessType = BusinessType.INSERT)
@PostMapping("/checkinAndConfirm")
public AjaxResult checkinAndConfirm(@RequestBody StudentCheckinAndConfirmDto dto)
{
    try {
        Long attendanceId = kgStudentAttendanceService.checkinAndConfirm(dto);
        if (attendanceId != null) {
            return AjaxResult.success("签到确认成功", attendanceId);
        } else {
            return AjaxResult.error("签到确认失败");
        }
    } catch (Exception e) {
        log.error("学生签到确认异常", e);
        return AjaxResult.error("签到确认异常：" + e.getMessage());
    }
}

// ========== Service接口新增方法 ==========
// 在文件: cl-kindergarten/src/main/java/com/cl/project/business/service/IKgStudentAttendanceService.java
// 在接口中添加以下方法:

/**
 * 学生签到并确认
 * 
 * @param dto 签到确认数据
 * @return 考勤记录ID
 */
public Long checkinAndConfirm(StudentCheckinAndConfirmDto dto);

// ========== Service实现类新增方法 ==========
// 在文件: cl-kindergarten/src/main/java/com/cl/project/business/service/impl/KgStudentAttendanceServiceImpl.java
// 在类中添加以下方法:

/**
 * 学生签到并确认
 * 
 * @param dto 签到确认数据
 * @return 考勤记录ID
 */
@Override
@Transactional
public Long checkinAndConfirm(StudentCheckinAndConfirmDto dto) {
    try {
        // 1. 创建考勤记录
        KgStudentAttendance attendance = new KgStudentAttendance();
        attendance.setStudentId(dto.getStudentId());
        
        // 解析考勤日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date attendanceDate = sdf.parse(dto.getAttendanceDate());
        attendance.setAttendanceDate(attendanceDate);
        
        // 设置签到时间为当前时间
        attendance.setCheckInTime(DateUtils.getNowDate());
        attendance.setAttendanceStatus(dto.getAttendanceStatus());
        attendance.setCheckInMethod("manual");
        attendance.setRemark(dto.getRemark());
        attendance.setCreateTime(DateUtils.getNowDate());
        
        // 2. 插入考勤记录
        int result = kgStudentAttendanceMapper.insertKgStudentAttendance(attendance);
        
        if (result > 0) {
            // 3. 自动确认考勤记录
            Long attendanceId = attendance.getAttendanceId();
            int confirmResult = confirmStudentAttendance(attendanceId);
            
            if (confirmResult > 0) {
                return attendanceId;
            } else {
                // 确认失败，抛出异常触发事务回滚
                throw new RuntimeException("考勤确认失败");
            }
        } else {
            // 插入失败，抛出异常
            throw new RuntimeException("考勤记录创建失败");
        }
        
    } catch (ParseException e) {
        log.error("日期解析失败", e);
        throw new RuntimeException("日期格式错误");
    } catch (Exception e) {
        log.error("学生签到确认失败", e);
        throw new RuntimeException("签到确认失败：" + e.getMessage());
    }
}

// ========== 需要添加的导入语句 ==========
// 在相应的文件中添加以下导入:

import com.cl.project.business.domain.dto.StudentCheckinAndConfirmDto;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.transaction.annotation.Transactional;
