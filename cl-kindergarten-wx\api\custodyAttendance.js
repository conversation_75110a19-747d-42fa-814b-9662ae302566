import request from '@/utils/request.js'

// 查询托管考勤概览
export function getCustodyAttendanceOverview(query) {
  return request.get('/business/course-attendance/overview', query)
}

// 查询托管考勤记录列表
export function listCustodyAttendance(query) {
  return request.get('/business/course-attendance/list', query)
}

// 查询托管考勤记录详细
export function getCustodyAttendance(attendanceId) {
  return request.get('/business/course-attendance/' + attendanceId)
}

// 新增托管考勤记录
export function addCustodyAttendance(data) {
  return request.post('/business/course-attendance', data)
}

// 修改托管考勤记录
export function updateCustodyAttendance(data) {
  return request.put('/business/course-attendance', data)
}

// 删除托管考勤记录
export function delCustodyAttendance(attendanceId) {
  return request.delete('/business/course-attendance/' + attendanceId)
}

// 托管签到
export function custodyCheckin(data) {
  return request.post('/business/course-attendance/checkin', data)
}

// 批量托管签到
export function batchCustodyCheckin(data) {
  return request.post('/business/course-attendance/batchCheckin', data)
}

// 确认托管考勤
export function confirmCustodyAttendance(attendanceId) {
  return request.put('/business/course-attendance/confirm/' + attendanceId)
}

// 批量确认托管考勤
export function batchConfirmCustodyAttendance(data) {
  return request.post('/business/course-attendance/batchConfirm', data)
}

// 托管签到并确认（组合接口）
export function custodyCheckinAndConfirm(data) {
  return request.post('/business/course-attendance/checkinAndConfirm', data)
}

// 导出托管考勤记录
export function exportCustodyAttendance(query) {
  return request.download('/business/course-attendance/export', query)
}

// 获取课程列表
export function getCourseList() {
  return request.get('/business/course/list')
}

// 获取所有活跃课程
export function getAllActiveCourses() {
  return request.get('/business/course/active')
}

// 获取学生报名的课程列表
export function getStudentEnrolledCourses(studentId) {
  return request.get('/business/course-enrollment/student/' + studentId)
}

// 同步钉钉托管考勤
export function syncDingtalkCustodyAttendance(data) {
  return request.post('/business/course-attendance/syncDingtalk', data)
}

// 获取钉钉托管考勤记录
export function getDingtalkCustodyRecords(query) {
  return request.get('/business/dingtalk-attendance/custody', query)
}
