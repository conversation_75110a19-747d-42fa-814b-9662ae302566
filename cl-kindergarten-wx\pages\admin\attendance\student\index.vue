<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">学生考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date" @click="showDatePickerDialog">
						<text class="date-text">{{ currentDate }}</text>
						<u-icon name="calendar" color="#667eea" size="16" class="calendar-icon"></u-icon>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view class="stat-card total">
					<view class="stat-icon">👥</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalStudents }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view class="stat-card present">
					<view class="stat-icon">✅</view>
					<view class="stat-info">
						<text class="stat-number">{{ presentCount }}</text>
						<text class="stat-label">已到园</text>
					</view>
				</view>
				<view class="stat-card leave">
					<view class="stat-icon">📝</view>
					<view class="stat-info">
						<text class="stat-number">{{ leaveCount }}</text>
						<text class="stat-label">请假</text>
					</view>
				</view>
				<view class="stat-card absent">
					<view class="stat-icon">❌</view>
					<view class="stat-info">
						<text class="stat-number">{{ absentCount }}</text>
						<text class="stat-label">未到园</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 班级筛选 -->
		<view class="filter-section">
			<view class="filter-header">
				<view class="filter-title">班级筛选</view>
				<view class="filter-actions">
					<view class="action-btn" @click="toggleAllClasses">
						<u-icon :name="isAllCollapsed ? 'arrow-down' : 'arrow-up'" color="#667eea" size="14"></u-icon>
						<text class="action-text">{{ isAllCollapsed ? '全部展开' : '全部折叠' }}</text>
					</view>
				</view>
			</view>
			<view class="filter-buttons">
				<view
					v-for="(classItem, index) in classFilter"
					:key="index"
					class="filter-btn"
					:class="{ active: selectedClass === classItem.value }"
					@click="filterByClass(classItem.value)"
				>
					<text class="filter-text">{{ classItem.label }}</text>
				</view>
			</view>
		</view>

		<!-- 考勤列表 -->
		<view class="attendance-list" v-if="!loading">
			<!-- 错误状态 -->
			<view v-if="errorMessage && attendanceData.length === 0" class="error-state">
				<view class="error-icon">⚠️</view>
				<text class="error-text">{{ errorMessage }}</text>
				<view class="error-actions">
					<view class="error-action" @click="loadAttendanceData">
						<text class="action-text">重新加载</text>
					</view>
					<view class="error-action secondary" @click="refreshData">
						<text class="action-text">刷新页面</text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="attendanceData.length === 0" class="empty-state">
				<view class="empty-icon">📋</view>
				<text class="empty-text">暂无考勤数据</text>
				<view class="empty-action" @click="loadAttendanceData">
					<text class="action-text">点击重新加载</text>
				</view>
			</view>

			<view v-for="classGroup in filteredAttendanceData" :key="classGroup.className" class="class-section">
				<view class="class-header" @click="toggleClassCollapse(classGroup.classValue)">
					<view class="class-icon">🏫</view>
					<view class="class-info">
						<text class="class-name">{{ classGroup.className }}</text>
						<text class="class-count">{{ classGroup.students.length }}名学生</text>
					</view>
					<view class="class-progress">
						<view class="progress-bar">
							<view
								class="progress-fill"
								:style="{ width: getClassProgress(classGroup) + '%' }"
							></view>
						</view>
						<text class="progress-text">{{ getClassProgress(classGroup) }}%</text>
					</view>
					<view class="collapse-icon" :class="{ collapsed: isClassCollapsed(classGroup.classValue) }">
						<u-icon name="arrow-down" color="#666666" size="16"></u-icon>
					</view>
				</view>

				<view class="students-grid" v-if="!isClassCollapsed(classGroup.classValue)">
					<view
						v-for="student in classGroup.students"
						:key="student.id"
						class="student-card"
						:class="{
							'selection-mode': isSelectionMode,
							'selected': selectedStudents.includes(student.id),
							'selectable': isSelectionMode && student.status === 'absent'
						}"
						@click="handleStudentClick(student)"
					>
						<view class="card-header">
							<view class="student-avatar" :class="student.status">
								<text class="avatar-text">{{ student.name.charAt(0) }}</text>
								<view class="status-indicator" :class="student.status"></view>
								<!-- 选择模式下的复选框 -->
								<view v-if="isSelectionMode && student.status === 'absent'" class="selection-checkbox" :class="{ checked: selectedStudents.includes(student.id) }">
									<u-icon v-if="selectedStudents.includes(student.id)" name="checkmark" color="#ffffff" size="12"></u-icon>
								</view>
							</view>
							<view class="student-basic">
								<text class="student-name">{{ student.name }}</text>
								<text class="student-no">{{ student.studentNo }}</text>
							</view>
							<view class="quick-actions">
								<view class="action-dot" @click="editAttendance(student)">
									<u-icon name="more-dot-fill" color="#cccccc" size="16"></u-icon>
								</view>
							</view>
						</view>

						<view class="card-content">
							<view class="time-info">
								<view class="time-item" v-if="student.checkInTime">
									<view class="time-icon checkin">🌅</view>
									<view class="time-details">
										<text class="time-label">入园时间</text>
										<text class="time-value">{{ student.checkInTime }}</text>
									</view>
								</view>
								<view class="time-item" v-if="student.checkOutTime">
									<view class="time-icon checkout">🌇</view>
									<view class="time-details">
										<text class="time-label">离园时间</text>
										<text class="time-value">{{ student.checkOutTime }}</text>
									</view>
								</view>
								<view class="time-item" v-if="!student.checkInTime && student.status === 'absent'">
									<view class="time-icon absent">⏰</view>
									<view class="time-details">
										<text class="time-label">状态</text>
										<text class="time-value absent">未到园</text>
									</view>
								</view>
								<view class="time-item" v-if="student.status === 'leave'">
									<view class="time-icon leave">📝</view>
									<view class="time-details">
										<text class="time-label">状态</text>
										<text class="time-value leave">请假</text>
										<text v-if="student.leaveReason" class="time-reason">{{ student.leaveReason }}</text>
									</view>
								</view>
							</view>

							<view class="status-badge" :class="student.status">
								<text class="status-text">{{ getStatusText(student.status) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-actions">
			<view class="fab-container">
				<view class="fab-btn secondary" @click="showSyncDialogMethod" v-if="!isSelectionMode">
					<u-icon name="reload" color="#ffffff" size="18"></u-icon>
					<text class="fab-text">同步钉钉</text>
				</view>
				<view class="fab-btn" :class="{ active: isSelectionMode }" @click="toggleSelectionMode">
					<u-icon :name="isSelectionMode ? 'checkmark' : 'checkbox'" color="#ffffff" size="20"></u-icon>
					<text class="fab-text">{{ isSelectionMode ? `确认签到(${selectedStudents.length})` : '批量签到' }}</text>
				</view>
			</view>
		</view>

		<!-- 选择模式提示 -->
		<view v-if="isSelectionMode" class="selection-tip">
			<view class="tip-content">
				<u-icon name="info-circle" color="#667eea" size="16"></u-icon>
				<text class="tip-text">请选择需要签到的学生</text>
				<view class="tip-actions">
					<text class="cancel-selection" @click="cancelSelection">取消</text>
				</view>
			</view>
		</view>

		<!-- 日期选择器 -->
		<CustomDatePicker
			:show="showDatePicker"
			:value="pickerValue"
			title="选择考勤日期"
			@confirm="onDateConfirm"
			@cancel="onDateCancel"
			@update:show="showDatePicker = $event">
		</CustomDatePicker>

		<!-- 同步钉钉对话框 -->
		<view v-if="showSyncDialog" class="sync-dialog-mask" @click="closeSyncDialog">
			<view class="sync-dialog" @click.stop="">
				<view class="dialog-header">
					<text class="dialog-title">同步钉钉考勤数据</text>
					<view class="dialog-close" @click="closeSyncDialog">
						<u-icon name="close" color="#999999" size="20"></u-icon>
					</view>
				</view>
				<view class="dialog-content">
					<view class="sync-info">
						<text class="info-text">将同步当前日期的钉钉打卡数据</text>
						<text class="date-info">日期：{{ currentDate }}</text>
					</view>
					<view class="sync-options">
						<view class="option-item">
							<text class="option-label">同步范围</text>
							<view class="option-value">
								<text class="value-text">所有学生</text>
							</view>
						</view>
					</view>
				</view>
				<view class="dialog-footer">
					<view class="dialog-btn cancel" @click="closeSyncDialog">
						<text class="btn-text">取消</text>
					</view>
					<view class="dialog-btn confirm" @click="confirmSync" :class="{ loading: syncLoading }">
						<view v-if="syncLoading" class="loading-spinner"></view>
						<text class="btn-text">{{ syncLoading ? '同步中...' : '开始同步' }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import {
	getStudentAttendanceOverview,
	batchStudentCheckin,
	confirmStudentAttendance,
	getClassList,
	getAllStudentList,
	getDingtalkAttendanceRecords,
	syncDingtalkAttendance
} from '@/api/studentAttendance.js'
import CustomDatePicker from '@/components/CustomDatePicker/CustomDatePicker.vue'

export default {
	components: {
		CustomDatePicker
	},
	data() {
		return {
			loading: false,
			currentDate: '',
			currentDateValue: '', // 用于API请求的日期格式
			selectedClass: 'all',
			isSelectionMode: false,
			selectedStudents: [],
			totalStudents: 0,
			presentCount: 0,
			absentCount: 0,
			leaveCount: 0,
			classFilter: [
				{ label: '全部', value: 'all' }
			],
			attendanceData: [],
			originalAttendanceData: [], // 保存原始数据用于筛选
			classList: [], // 班级列表
			studentList: [], // 学生列表
			errorMessage: '', // 错误信息
			retryCount: 0, // 重试次数
			maxRetries: 3, // 最大重试次数
			collapsedClasses: [], // 折叠的班级ID数组
			// 日期选择器相关
			showDatePicker: false,
			pickerValue: '',
			// 同步钉钉相关
			showSyncDialog: false,
			syncLoading: false
		}
	},
	computed: {
		filteredAttendanceData() {
			if (this.selectedClass === 'all') {
				return this.attendanceData
			}
			return this.attendanceData.filter(classGroup => classGroup.classValue === this.selectedClass)
		},

		// 判断是否所有班级都已折叠
		isAllCollapsed() {
			if (this.attendanceData.length === 0) return false
			return this.attendanceData.every(classGroup =>
				this.collapsedClasses.includes(String(classGroup.classValue))
			)
		}
	},
	onLoad() {
		this.initCurrentDate()
		this.initDatePicker()
		this.loadClassList()
		this.loadAttendanceData()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.refreshData()
	},

	// 页面显示时刷新数据
	onShow() {
		// 如果数据已加载过，则静默刷新
		if (this.attendanceData.length > 0) {
			this.loadAttendanceData(false)
		}
	},
	methods: {
		initCurrentDate() {
			const now = new Date()
			const dateInfo = this.formatDateDisplay(now)
			this.currentDate = dateInfo.display
			this.currentDateValue = dateInfo.value
		},

		// 格式化日期显示
		formatDateDisplay(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[date.getDay()]

			return {
				display: `${year}年${month}月${day}日 周${weekday}`,
				value: `${year}-${month}-${day}`
			}
		},

		// 初始化日期选择器
		initDatePicker() {
			this.pickerValue = new Date(this.currentDateValue).getTime()
		},

		// 显示日期选择器
		showDatePickerDialog() {
			this.showDatePicker = true
		},

		// 日期选择确认
		onDateConfirm(e) {
			const selectedDate = new Date(e.value)
			const dateInfo = this.formatDateDisplay(selectedDate)

			this.currentDate = dateInfo.display
			this.currentDateValue = dateInfo.value
			this.pickerValue = e.value
			this.showDatePicker = false

			// 重新加载数据
			this.loadAttendanceData()
		},

		// 日期选择取消
		onDateCancel() {
			this.showDatePicker = false
		},

		// 加载班级列表
		async loadClassList() {
			try {
				const response = await getClassList({ pageNum: 1, pageSize: 100 })
				if (response.code === 200) {
					this.classList = response.rows || []
					// 更新班级筛选选项
					this.classFilter = [
						{ label: '全部', value: 'all' },
						...this.classList.map(cls => ({
							label: cls.className,
							value: cls.classId.toString()
						}))
					]
				} else {
					console.error('获取班级列表失败:', response.msg)
				}
			} catch (error) {
				console.error('加载班级列表失败:', error)
				// 如果班级列表加载失败，使用默认筛选选项
				this.classFilter = [{ label: '全部', value: 'all' }]
			}
		},

		// 加载考勤数据
		async loadAttendanceData(shouldRerender = true) {
			try {
				if (shouldRerender) {
					this.loading = true
					this.errorMessage = ''
				}

				const params = {
					attendanceDate: this.currentDateValue,
					pageNum: 1,
					pageSize: 200 // 小程序端一次性加载所有数据
				}

				const response = await getStudentAttendanceOverview(params)
				if (response.code === 200) {
					this.originalAttendanceData = this.formatAttendanceData(response.rows || [])
					this.filterAttendanceData()
					this.calculateStats()
					this.retryCount = 0 // 重置重试次数
					
					// 测试：预先折叠第一个班级
					if (this.attendanceData.length > 0 && this.collapsedClasses.length === 0) {
						console.log('测试：预先折叠第一个班级')
						this.collapsedClasses = [String(this.attendanceData[0].classValue)]
					}
				} else {
					throw new Error(response.msg || '获取数据失败')
				}
			} catch (error) {
				console.error('加载考勤数据失败:', error)
				this.errorMessage = this.getErrorMessage(error)

				if (shouldRerender) {
					// 如果是网络错误且未达到最大重试次数，提供重试选项
					if (this.isNetworkError(error) && this.retryCount < this.maxRetries) {
						uni.showModal({
							title: '网络错误',
							content: `加载失败，是否重试？(${this.retryCount + 1}/${this.maxRetries})`,
							success: (res) => {
								if (res.confirm) {
									this.retryCount++
									setTimeout(() => {
										this.loadAttendanceData(shouldRerender)
									}, 1000)
								}
							}
						})
					} else {
						toast(this.errorMessage)
					}
				}
			} finally {
				if (shouldRerender) {
					this.loading = false
				}
			}
		},

		// 格式化考勤数据
		formatAttendanceData(data) {
			const classMap = new Map()

			data.forEach(item => {
				const classId = item.classId
				const className = item.className || '未分班'

				if (!classMap.has(classId)) {
					classMap.set(classId, {
						className: className,
						classValue: String(classId || 'unassigned'), // 确保是字符串
						students: []
					})
				}

				const student = {
					id: item.studentId,
					name: item.studentName,
					studentNo: item.studentNo || '',
					checkInTime: item.checkInTime ? this.formatTime(item.checkInTime) : '',
					checkOutTime: item.checkOutTime ? this.formatTime(item.checkOutTime) : '',
					status: this.mapAttendanceStatus(item.attendanceStatus),
					attendanceId: item.attendanceId,
					leaveReason: item.remark || '',
					dingtalkRecords: item.dingtalkRecords || []
				}

				classMap.get(classId).students.push(student)
			})

			const result = Array.from(classMap.values())
			console.log('格式化后的数据:', result)
			return result
		},

		// 映射考勤状态
		mapAttendanceStatus(status) {
			const statusMap = {
				'1': 'present',  // 出勤
				'2': 'leave',    // 请假
				'3': 'absent'    // 缺勤
			}
			return statusMap[status] || 'absent'
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return ''
			const date = new Date(timeStr)
			return date.toLocaleTimeString('zh-CN', {
				hour12: false,
				hour: '2-digit',
				minute: '2-digit'
			})
		},

		// 筛选考勤数据
		filterAttendanceData() {
			if (this.selectedClass === 'all') {
				this.attendanceData = this.originalAttendanceData
			} else {
				this.attendanceData = this.originalAttendanceData.filter(
					classGroup => classGroup.classValue === this.selectedClass
				)
			}
		},

		// 刷新数据
		async refreshData() {
			try {
				await Promise.all([
					this.loadClassList(),
					this.loadAttendanceData(false)
				])
				toast('刷新成功')
			} catch (error) {
				console.error('刷新失败:', error)
				toast('刷新失败，请重试')
			} finally {
				// 停止下拉刷新
				uni.stopPullDownRefresh()
			}
		},
		
		goBack() {
			uni.navigateBack()
		},

		changeDate(direction) {
			const currentDate = new Date(this.currentDateValue)
			currentDate.setDate(currentDate.getDate() + direction)

			const year = currentDate.getFullYear()
			const month = String(currentDate.getMonth() + 1).padStart(2, '0')
			const day = String(currentDate.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[currentDate.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateValue = `${year}-${month}-${day}`

			// 重新加载数据
			this.loadAttendanceData()
		},

		filterByClass(classValue) {
			this.selectedClass = classValue
			this.filterAttendanceData()
		},

		// 检查班级是否折叠
		isClassCollapsed(classValue) {
			const result = this.collapsedClasses.includes(String(classValue))
			console.log(`检查班级 ${classValue} 是否折叠:`, result, '折叠列表:', this.collapsedClasses)
			return result
		},

		// 切换班级折叠状态
		toggleClassCollapse(classValue) {
			console.log('切换班级折叠状态:', classValue, '当前折叠列表:', this.collapsedClasses)
			
			// 确保classValue是字符串类型
			const classValueStr = String(classValue)
			const index = this.collapsedClasses.indexOf(classValueStr)
			
			// 创建新数组来触发响应式更新
			let newCollapsedClasses = [...this.collapsedClasses]
			
			if (index > -1) {
				// 展开：从数组中移除
				newCollapsedClasses.splice(index, 1)
				console.log('展开班级:', classValueStr)
			} else {
				// 折叠：添加到数组中
				newCollapsedClasses.push(classValueStr)
				console.log('折叠班级:', classValueStr)
			}
			
			// 直接替换数组以确保响应式更新
			this.collapsedClasses = newCollapsedClasses
			console.log('更新后的折叠列表:', this.collapsedClasses)
			
			// 强制更新组件
			this.$forceUpdate()
		},

		// 全部展开/折叠
		toggleAllClasses() {
			if (this.isAllCollapsed) {
				// 全部展开：清空数组
				this.collapsedClasses.splice(0, this.collapsedClasses.length)
				toast('已展开所有班级')
			} else {
				// 全部折叠：添加所有班级ID，确保是字符串类型
				const allClassValues = this.attendanceData.map(classGroup => String(classGroup.classValue))
				this.collapsedClasses.splice(0, this.collapsedClasses.length, ...allClassValues)
				toast('已折叠所有班级')
			}
		},

		// 显示同步钉钉对话框
		showSyncDialogMethod() {
			this.showSyncDialog = true
		},

		// 关闭同步钉钉对话框
		closeSyncDialog() {
			if (!this.syncLoading) {
				this.showSyncDialog = false
			}
		},

		// 确认同步钉钉数据
		async confirmSync() {
			if (this.syncLoading) return

			try {
				this.syncLoading = true

				// 准备同步数据
				const syncData = {
					workDateFrom: `${this.currentDateValue} 00:00:00`,
					workDateTo: `${this.currentDateValue} 23:59:59`,
					userIdList: [] // 空数组表示同步所有用户
				}

				const response = await syncDingtalkAttendance(syncData)

				if (response.code === 200) {
					toast(`同步成功，共同步 ${response.data || 0} 条记录`)
					this.closeSyncDialog()
					// 重新加载考勤数据
					await this.loadAttendanceData()
				} else {
					toast('同步失败：' + (response.msg || '未知错误'))
				}
			} catch (error) {
				console.error('同步钉钉数据失败:', error)
				toast('同步失败，请重试')
			} finally {
				this.syncLoading = false
			}
		},

		getStatusText(status) {
			const statusMap = {
				present: '已到园',
				absent: '未到园',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},

		getClassProgress(classGroup) {
			const presentStudents = classGroup.students.filter(s => s.status === 'present').length
			return Math.round((presentStudents / classGroup.students.length) * 100)
		},
		
		editAttendance(student) {
			toast(`编辑 ${student.name} 的考勤记录`)
		},
		
		toggleSelectionMode() {
			if (this.isSelectionMode) {
				// 确认批量签到
				if (this.selectedStudents.length === 0) {
					toast('请选择需要签到的学生')
					return
				}
				this.batchCheckIn()
			} else {
				// 进入选择模式
				this.isSelectionMode = true
				this.selectedStudents = []
			}
		},

		cancelSelection() {
			this.isSelectionMode = false
			this.selectedStudents = []
		},

		handleStudentClick(student) {
			if (this.isSelectionMode) {
				if (student.status === 'absent') {
					// 切换选择状态
					const index = this.selectedStudents.indexOf(student.id)
					if (index > -1) {
						this.selectedStudents.splice(index, 1)
					} else {
						this.selectedStudents.push(student.id)
					}
				}
			} else {
				// 非选择模式，编辑考勤
				this.editAttendance(student)
			}
		},

		async batchCheckIn() {
			if (this.selectedStudents.length === 0) {
				toast('请选择需要签到的学生')
				return
			}

			uni.showModal({
				title: '确认签到',
				content: `确定为选中的 ${this.selectedStudents.length} 名学生签到吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '签到中...' })

							// 准备批量签到数据
							const batchData = {
								studentIds: this.selectedStudents.map(id => id.toString()),
								attendanceDate: this.currentDateValue,
								attendanceStatus: '1', // 1-出勤
								checkInMethod: 'manual'
							}

							const response = await batchStudentCheckin(batchData)

							if (response.code === 200) {
								toast(`成功为 ${response.data || this.selectedStudents.length} 名学生签到`)
								// 重新加载数据
								await this.loadAttendanceData()
								// 退出选择模式
								this.cancelSelection()
							} else {
								toast('签到失败：' + (response.msg || '未知错误'))
							}
						} catch (error) {
							console.error('批量签到失败:', error)
							toast('签到失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},

		calculateStats() {
			let total = 0
			let present = 0
			let absent = 0
			let leave = 0

			this.attendanceData.forEach(classGroup => {
				classGroup.students.forEach(student => {
					total++
					if (student.status === 'present') {
						present++
					} else if (student.status === 'absent') {
						absent++
					} else if (student.status === 'leave') {
						leave++
					}
				})
			})

			this.totalStudents = total
			this.presentCount = present
			this.absentCount = absent
			this.leaveCount = leave
		},

		// 编辑考勤记录
		editAttendance(student) {
			// 可以跳转到考勤详情页面或显示编辑弹窗
			uni.showActionSheet({
				itemList: ['确认考勤', '标记请假', '查看详情'],
				success: async (res) => {
					switch (res.tapIndex) {
						case 0:
							await this.confirmAttendance(student)
							break
						case 1:
							this.markAsLeave(student)
							break
						case 2:
							this.showAttendanceDetail(student)
							break
					}
				}
			})
		},

		// 确认考勤
		async confirmAttendance(student) {
			if (!student.attendanceId) {
				toast('该学生暂无考勤记录')
				return
			}

			try {
				uni.showLoading({ title: '确认中...' })
				const response = await confirmStudentAttendance(student.attendanceId)

				if (response.code === 200) {
					toast('考勤确认成功')
					await this.loadAttendanceData(false) // 静默刷新
				} else {
					toast('确认失败：' + (response.msg || '未知错误'))
				}
			} catch (error) {
				console.error('确认考勤失败:', error)
				toast('确认失败，请重试')
			} finally {
				uni.hideLoading()
			}
		},

		// 标记请假
		markAsLeave(student) {
			uni.showModal({
				title: '标记请假',
				content: `确定将 ${student.name} 标记为请假吗？`,
				success: (res) => {
					if (res.confirm) {
						// 这里可以调用请假登记接口
						toast('请假标记成功')
					}
				}
			})
		},

		// 显示考勤详情
		showAttendanceDetail(student) {
			const details = [
				`姓名：${student.name}`,
				`学号：${student.studentNo}`,
				`状态：${this.getStatusText(student.status)}`,
				student.checkInTime ? `入园时间：${student.checkInTime}` : '',
				student.checkOutTime ? `离园时间：${student.checkOutTime}` : '',
				student.leaveReason ? `备注：${student.leaveReason}` : ''
			].filter(item => item).join('\n')

			uni.showModal({
				title: '考勤详情',
				content: details,
				showCancel: false
			})
		},

		// 获取错误信息
		getErrorMessage(error) {
			if (typeof error === 'string') {
				return error
			}

			if (error.message) {
				return error.message
			}

			// 根据错误类型返回友好的错误信息
			if (this.isNetworkError(error)) {
				return '网络连接失败，请检查网络设置'
			}

			if (error.code === 401) {
				return '登录已过期，请重新登录'
			}

			if (error.code === 403) {
				return '没有权限访问该功能'
			}

			if (error.code >= 500) {
				return '服务器错误，请稍后重试'
			}

			return '操作失败，请重试'
		},

		// 判断是否为网络错误
		isNetworkError(error) {
			return error.code === 'NETWORK_ERROR' ||
				   error.message?.includes('网络') ||
				   error.message?.includes('timeout') ||
				   error.message?.includes('Network') ||
				   !navigator.onLine
		},

		// 安全的toast提示
		safeToast(message, duration = 2000) {
			try {
				if (typeof message === 'string' && message.trim()) {
					toast(message)
				}
			} catch (error) {
				console.error('Toast显示失败:', error)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 加载状态 */
.loading-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.subtitle-text {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 400;
}

/* 日期统计卡片 */
.date-stats-card {
	margin: 30rpx;
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.date-section {
	margin-bottom: 40rpx;
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(102, 126, 234, 0.2);
	}
}

.current-date {
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		opacity: 0.8;
	}
}

.calendar-icon {
	opacity: 0.7;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.stats-section {
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }
	}

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		&::before { background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%); }
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }
	}
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 筛选区域 */
.filter-section {
	margin: 0 30rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.filter-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.filter-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.filter-actions {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 20rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(102, 126, 234, 0.2);
	}
}

.action-text {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 500;
}

.filter-buttons {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.filter-btn {
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	}
}

.filter-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #666666;

	.filter-btn.active & {
		color: #ffffff;
	}
}

/* 考勤列表 */
.attendance-list {
	padding: 0 30rpx 200rpx;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 60rpx;
	background: #ffffff;
	border-radius: 24rpx;
	margin: 30rpx 0;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
	margin-bottom: 40rpx;
	font-weight: 500;
}

.empty-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		opacity: 0.8;
	}
}

.action-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 600;
}

/* 错误状态 */
.error-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 60rpx;
	background: #ffffff;
	border-radius: 24rpx;
	margin: 30rpx 0;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid #ffebee;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
	opacity: 0.8;
}

.error-text {
	font-size: 32rpx;
	color: #f44336;
	margin-bottom: 40rpx;
	font-weight: 500;
	text-align: center;
	line-height: 1.5;
}

.error-actions {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
	justify-content: center;
}

.error-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
	border-radius: 50rpx;
	transition: all 0.3s ease;

	&.secondary {
		background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
	}

	&:active {
		transform: scale(0.95);
		opacity: 0.8;
	}
}

.class-section {
	margin-bottom: 40rpx;
}

.class-header {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	cursor: pointer;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.class-icon {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.class-info {
	flex: 1;
}

.class-name {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.class-count {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

.class-progress {
	width: 120rpx;
	text-align: center;
}

.progress-bar {
	width: 100%;
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin-bottom: 8rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 20rpx;
	color: #666666;
	font-weight: 600;
}

.collapse-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.collapsed {
		transform: rotate(-90deg);
		background: #e9ecef;
	}
}

/* 学生网格 */
.students-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20rpx;
}

.student-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.card-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 24rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	font-weight: 700;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		color: #4caf50;
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		color: #f44336;
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		color: #ff9800;
	}
}

.avatar-text {
	font-size: 28rpx;
	font-weight: 700;
}

.status-indicator {
	position: absolute;
	bottom: 4rpx;
	right: 4rpx;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	border: 3rpx solid #ffffff;

	&.present { background: #4caf50; }
	&.absent { background: #f44336; }
	&.leave { background: #ff9800; }
}

.student-basic {
	flex: 1;
}

.student-name {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.student-no {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

.quick-actions {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: #e9ecef;
	}
}

.card-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.time-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;

	&.checkin {
		background: rgba(76, 175, 80, 0.1);
	}

	&.checkout {
		background: rgba(255, 152, 0, 0.1);
	}

	&.absent {
		background: rgba(244, 67, 54, 0.1);
	}

	&.leave {
		background: rgba(255, 152, 0, 0.1);
	}
}

.time-details {
	flex: 1;
}

.time-label {
	display: block;
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.time-value {
	display: block;
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;

	&.absent {
		color: #f44336;
	}

	&.leave {
		color: #ff9800;
	}
}

.time-reason {
	display: block;
	font-size: 22rpx;
	color: #999999;
	margin-top: 4rpx;
	font-weight: 400;
}

.status-badge {
	padding: 12rpx 20rpx;
	border-radius: 50rpx;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
	}
}

.status-text {
	font-size: 22rpx;
	font-weight: 600;

	.status-badge.present & { color: #4caf50; }
	.status-badge.absent & { color: #f44336; }
	.status-badge.leave & { color: #ff9800; }
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.fab-container {
	display: flex;
	gap: 20rpx;
}

.fab-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&.secondary {
		flex: 0 0 120rpx;
		background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
		box-shadow: 0 16rpx 40rpx rgba(108, 117, 125, 0.4);
		margin-right: 20rpx;

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(108, 117, 125, 0.6);
		}
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);

		&::before {
			left: 100%;
		}
	}

	&.active {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}
	}
}

.fab-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 选择模式样式 */
.student-card {
	&.selection-mode {
		&.selectable {
			border: 2rpx solid rgba(102, 126, 234, 0.3);

			&:active {
				transform: scale(0.98);
			}

			&.selected {
				border-color: #667eea;
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
			}
		}

		&:not(.selectable) {
			opacity: 0.6;
			pointer-events: none;
		}
	}
}

.selection-checkbox {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ffffff;
	border: 3rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		background: #667eea;
		border-color: #667eea;
		transform: scale(1.1);
	}
}

.selection-tip {
	position: fixed;
	top: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 99;
}

.tip-content {
	background: rgba(0, 0, 0, 0.8);
	color: #ffffff;
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	backdrop-filter: blur(10rpx);
	animation: tipFadeIn 0.3s ease;
}

.tip-text {
	font-size: 26rpx;
	font-weight: 500;
}

.tip-actions {
	margin-left: 20rpx;
}

.cancel-selection {
	font-size: 24rpx;
	color: #ff6b6b;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(255, 107, 107, 0.2);
}

@keyframes tipFadeIn {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-20rpx);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}

/* 同步钉钉对话框 */
.sync-dialog-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease;
}

.sync-dialog {
	width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	animation: slideUp 0.3s ease;
}

.dialog-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.dialog-close {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: #e9ecef;
	}
}

.dialog-content {
	padding: 40rpx;
}

.sync-info {
	margin-bottom: 40rpx;
}

.info-text {
	display: block;
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 16rpx;
	line-height: 1.5;
}

.date-info {
	display: block;
	font-size: 26rpx;
	color: #667eea;
	font-weight: 600;
}

.sync-options {
	border: 1rpx solid #f0f0f0;
	border-radius: 16rpx;
	overflow: hidden;
}

.option-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background: #fafafa;
}

.option-label {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.option-value {
	display: flex;
	align-items: center;
}

.value-text {
	font-size: 26rpx;
	color: #667eea;
	font-weight: 600;
}

.dialog-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
}

.dialog-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	transition: all 0.3s ease;

	&.cancel {
		background: #f8f9fa;
		border: 1rpx solid #e9ecef;

		&:active {
			transform: scale(0.95);
			background: #e9ecef;
		}

		.btn-text {
			color: #666666;
		}
	}

	&.confirm {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

		&:active {
			transform: scale(0.95);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
		}

		&.loading {
			opacity: 0.8;
			pointer-events: none;
		}

		.btn-text {
			color: #ffffff;
		}
	}
}

.btn-text {
	font-size: 28rpx;
	font-weight: 600;
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
	border-top: 3rpx solid #ffffff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>
