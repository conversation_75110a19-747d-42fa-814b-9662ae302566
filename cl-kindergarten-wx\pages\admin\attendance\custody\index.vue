<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">托管考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date" @click="showDatePickerDialog">
						<text class="date-text">{{ currentDate }}</text>
						<u-icon name="calendar" color="#667eea" size="16" class="calendar-icon"></u-icon>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view
					class="stat-card total"
					:class="{ active: currentFilter === 'all' }"
					@click="setFilter('all')"
				>
					<view class="stat-icon">👥</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalStudents }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view
					class="stat-card checked-in"
					:class="{ active: currentFilter === 'checked-in' }"
					@click="setFilter('checked-in')"
				>
					<view class="stat-icon">🏢</view>
					<view class="stat-info">
						<text class="stat-number">{{ custodyCheckedInCount }}</text>
						<text class="stat-label">托管签到</text>
					</view>
				</view>
				<view
					class="stat-card not-checked-in"
					:class="{ active: currentFilter === 'not-checked-in' }"
					@click="setFilter('not-checked-in')"
				>
					<view class="stat-icon">⏰</view>
					<view class="stat-info">
						<text class="stat-number">{{ custodyNotCheckedInCount }}</text>
						<text class="stat-label">未托管</text>
					</view>
				</view>
				<view
					class="stat-card leave"
					:class="{ active: currentFilter === 'leave' }"
					@click="setFilter('leave')"
				>
					<view class="stat-icon">🚪</view>
					<view class="stat-info">
						<text class="stat-number">{{ leaveCount }}</text>
						<text class="stat-label">已离园</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-container">
				<view class="search-input-wrapper">
					<u-icon name="search" color="#999" size="16" class="search-icon"></u-icon>
					<input 
						class="search-input" 
						type="text" 
						placeholder="搜索学生姓名" 
						v-model="searchKeyword"
						@input="handleSearch"
					/>
					<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
						<u-icon name="close-circle-fill" color="#ccc" size="16"></u-icon>
					</view>
				</view>
				<view class="filter-btn" @click="showClassFilter">
					<u-icon name="filter" color="#667eea" size="14"></u-icon>
					<text class="filter-text">{{ selectedClass ? selectedClass.className : '班级' }}</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="operation-buttons">
			<view class="operation-btn batch-checkin" @click="showBatchCheckinDialog">
				<u-icon name="plus-circle" color="#ffffff" size="16"></u-icon>
				<text class="btn-text">批量托管签到</text>
			</view>
			<view class="operation-btn batch-checkout" @click="showBatchCheckoutDialog">
				<u-icon name="minus-circle" color="#ffffff" size="16"></u-icon>
				<text class="btn-text">批量离园</text>
			</view>
		</view>

		<!-- 学生列表 -->
		<view class="student-list">
			<view v-if="filteredStudents.length === 0" class="empty-state">
				<view class="empty-icon">🔍</view>
				<text class="empty-text">暂无数据</text>
			</view>
			<view 
				v-for="student in filteredStudents" 
				:key="student.studentId" 
				class="student-card"
				:class="getStudentCardClass(student)"
			>
				<view class="student-info">
					<view class="student-avatar">
						<text class="avatar-text">{{ student.studentName.charAt(0) }}</text>
					</view>
					<view class="student-details">
						<view class="student-name-row">
							<text class="student-name">{{ student.studentName }}</text>
							<view class="status-badge" :class="getCustodyStatusClass(student)">
								<text>{{ getCustodyStatusText(student) }}</text>
							</view>
						</view>
						<text class="student-class">{{ student.className }}</text>
						<view class="custody-time" v-if="student.custodyCheckinTime">
							<text class="time-label">托管时间：</text>
							<text class="time-value">{{ student.custodyCheckinTime }}</text>
						</view>
						<view class="leave-time" v-if="student.leaveTime">
							<text class="time-label">离园时间：</text>
							<text class="time-value">{{ student.leaveTime }}</text>
						</view>
					</view>
				</view>
				<view class="student-actions">
					<view 
						v-if="!student.custodyCheckinTime"
						class="action-btn custody-checkin"
						@click="handleCustodyCheckin(student)"
					>
						<text>托管签到</text>
					</view>
					<view 
						v-if="student.custodyCheckinTime && !student.leaveTime"
						class="action-btn leave"
						@click="handleLeave(student)"
					>
						<text>离园</text>
					</view>
					<view class="action-btn detail" @click="showStudentDetail(student)">
						<text>详情</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器弹窗 -->
		<u-calendar 
			v-model="showDatePicker" 
			mode="date"
			@confirm="onDatePickerConfirm"
		></u-calendar>

		<!-- 班级筛选弹窗 -->
		<u-action-sheet 
			v-model="showClassFilterPopup" 
			:list="classActionList"
			@click="selectClassFromAction"
		></u-action-sheet>
	</view>
</template>

<script>
import { useRouter, toast } from '@/utils/utils.js';
import { getCustodyAttendanceOverview, custodyCheckin, custodyLeave, batchCustodyCheckin, batchCustodyLeave } from '@/api/custodyAttendance.js';
import { getClassList } from '@/api/studentClass.js';

export default {
	data() {
		return {
			loading: false,
			currentDate: '',
			searchKeyword: '',
			currentFilter: 'all',
			selectedClass: null,
			classList: [],
			studentList: [],
			selectedStudents: [],
			
			// 统计数据
			totalStudents: 0,
			custodyCheckedInCount: 0,
			custodyNotCheckedInCount: 0,
			leaveCount: 0,
			
			// 弹窗状态
			showDatePicker: false,
			showClassFilterPopup: false,
			
			// 班级选择列表
			classActionList: []
		};
	},
	computed: {
		filteredStudents() {
			let students = this.studentList;
			
			// 按班级筛选
			if (this.selectedClass) {
				students = students.filter(student => student.classId === this.selectedClass.classId);
			}
			
			// 按关键词搜索
			if (this.searchKeyword) {
				students = students.filter(student => 
					student.studentName.includes(this.searchKeyword)
				);
			}
			
			// 按状态筛选
			switch (this.currentFilter) {
				case 'checked-in':
					students = students.filter(student => student.custodyCheckinTime && !student.leaveTime);
					break;
				case 'not-checked-in':
					students = students.filter(student => !student.custodyCheckinTime);
					break;
				case 'leave':
					students = students.filter(student => student.leaveTime);
					break;
			}
			
			return students;
		}
	},
	onLoad() {
		this.initCurrentDate();
		this.loadClassList();
		this.loadCustodyAttendance();
	},
	methods: {
		// 初始化当前日期
		initCurrentDate() {
			const today = new Date();
			this.currentDate = this.formatDate(today);
		},
		
		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 改变日期
		changeDate(days) {
			const currentDateObj = new Date(this.currentDate);
			currentDateObj.setDate(currentDateObj.getDate() + days);
			this.currentDate = this.formatDate(currentDateObj);
			this.loadCustodyAttendance();
		},
		
		// 显示日期选择器
		showDatePickerDialog() {
			this.showDatePicker = true;
		},
		
		// 日期选择器确认
		onDatePickerConfirm(e) {
			this.currentDate = e.result;
			this.loadCustodyAttendance();
		},
		
		// 设置筛选条件
		setFilter(filter) {
			this.currentFilter = filter;
		},
		
		// 处理搜索
		handleSearch() {
			// 搜索逻辑在computed中实现
		},
		
		// 清除搜索
		clearSearch() {
			this.searchKeyword = '';
		},
		
		// 显示班级筛选
		showClassFilter() {
			this.showClassFilterPopup = true;
		},
		
		// 选择班级
		selectClass(classItem) {
			this.selectedClass = classItem;
			this.updateClassActionList();
		},
		
		// 从action sheet选择班级
		selectClassFromAction(index) {
			if (index === 0) {
				this.selectedClass = null;
			} else {
				this.selectedClass = this.classList[index - 1];
			}
			this.showClassFilterPopup = false;
		},
		
		// 显示批量托管签到弹窗
		showBatchCheckinDialog() {
			uni.showModal({
				title: '批量托管签到',
				content: '确定要为选中的学生进行托管签到吗？',
				success: (res) => {
					if (res.confirm) {
						this.confirmBatchCustodyCheckin();
					}
				}
			});
		},
		
		// 显示批量离园弹窗
		showBatchCheckoutDialog() {
			uni.showModal({
				title: '批量离园',
				content: '确定要为选中的学生办理离园吗？',
				success: (res) => {
					if (res.confirm) {
						this.confirmBatchLeave();
					}
				}
			});
		},
		
		// 托管签到
		async handleCustodyCheckin(student) {
			try {
				const result = await custodyCheckin({
					studentId: student.studentId,
					attendanceDate: this.currentDate
				});
				toast('托管签到成功');
				this.loadCustodyAttendance();
			} catch (error) {
				toast('托管签到失败');
			}
		},
		
		// 离园
		async handleLeave(student) {
			try {
				const result = await custodyLeave({
					studentId: student.studentId,
					attendanceDate: this.currentDate
				});
				toast('离园成功');
				this.loadCustodyAttendance();
			} catch (error) {
				toast('离园失败');
			}
		},
		
		// 批量托管签到确认
		async confirmBatchCustodyCheckin() {
			try {
				const studentIds = this.selectedStudents.map(s => s.studentId);
				const result = await batchCustodyCheckin({
					studentIds,
					attendanceDate: this.currentDate
				});
				toast('批量托管签到成功');
				this.selectedStudents = [];
				this.loadCustodyAttendance();
			} catch (error) {
				toast('批量托管签到失败');
			}
		},
		
		// 批量离园确认
		async confirmBatchLeave() {
			try {
				const studentIds = this.selectedStudents.map(s => s.studentId);
				const result = await batchCustodyLeave({
					studentIds,
					attendanceDate: this.currentDate
				});
				toast('批量离园成功');
				this.selectedStudents = [];
				this.loadCustodyAttendance();
			} catch (error) {
				toast('批量离园失败');
			}
		},
		
		// 显示学生详情
		showStudentDetail(student) {
			// TODO: 跳转到学生详情页面
			toast('学生详情功能待开发');
		},
		
		// 加载班级列表
		async loadClassList() {
			try {
				const result = await getClassList();
				this.classList = result.data || [];
				this.updateClassActionList();
			} catch (error) {
				console.error('加载班级列表失败:', error);
			}
		},
		
		// 更新班级选择列表
		updateClassActionList() {
			this.classActionList = [
				{ text: '全部班级' },
				...this.classList.map(item => ({ text: item.className }))
			];
		},
		
		// 加载托管考勤数据
		async loadCustodyAttendance() {
			try {
				this.loading = true;
				const params = {
					attendanceDate: this.currentDate,
					classId: this.selectedClass ? this.selectedClass.classId : null
				};
				
				const result = await getCustodyAttendanceOverview(params);
				this.studentList = result.data.students || [];
				this.updateStatistics();
			} catch (error) {
				console.error('加载托管考勤数据失败:', error);
				toast('加载数据失败');
			} finally {
				this.loading = false;
			}
		},
		
		// 更新统计数据
		updateStatistics() {
			this.totalStudents = this.studentList.length;
			this.custodyCheckedInCount = this.studentList.filter(s => s.custodyCheckinTime && !s.leaveTime).length;
			this.custodyNotCheckedInCount = this.studentList.filter(s => !s.custodyCheckinTime).length;
			this.leaveCount = this.studentList.filter(s => s.leaveTime).length;
		},
		
		// 获取托管状态样式类
		getCustodyStatusClass(student) {
			if (student.leaveTime) {
				return 'status-leave';
			} else if (student.custodyCheckinTime) {
				return 'status-custody';
			} else {
				return 'status-not-custody';
			}
		},
		
		// 获取托管状态文本
		getCustodyStatusText(student) {
			if (student.leaveTime) {
				return '已离园';
			} else if (student.custodyCheckinTime) {
				return '托管中';
			} else {
				return '未托管';
			}
		},
		
		// 获取学生卡片样式类
		getStudentCardClass(student) {
			if (student.leaveTime) {
				return 'card-leave';
			} else if (student.custodyCheckinTime) {
				return 'card-custody';
			} else {
				return 'card-not-custody';
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

/* 头部导航栏样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 40rpx;
	position: sticky;
	top: 0;
	z-index: 1000;
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.header-title {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 加载状态样式 */
.loading-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.loading-content {
	text-align: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: #666;
	font-size: 28rpx;
}

/* 日期和统计卡片样式 */
.date-stats-card {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.date-section {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
}

.current-date {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.stats-section {
	display: flex;
	padding: 20rpx;
	gap: 10rpx;
}

.stat-card {
	flex: 1;
	padding: 20rpx 16rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	text-align: center;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	
	&.active {
		border-color: #667eea;
		background: rgba(102, 126, 234, 0.1);
	}
}

.stat-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.stat-number {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 4rpx;
}

.stat-label {
	font-size: 20rpx;
	color: #666;
}

/* 搜索栏样式 */
.search-section {
	padding: 0 20rpx 20rpx;
}

.search-container {
	display: flex;
	gap: 20rpx;
	align-items: center;
}

.search-input-wrapper {
	flex: 1;
	position: relative;
	background: #ffffff;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
}

.search-icon {
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	height: 72rpx;
	font-size: 28rpx;
	color: #333;
	border: none;
	outline: none;
	background: transparent;
}

.clear-icon {
	margin-left: 16rpx;
}

.filter-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx;
	background: #ffffff;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
}

.filter-text {
	font-size: 24rpx;
	color: #667eea;
}

/* 操作按钮样式 */
.operation-buttons {
	display: flex;
	gap: 20rpx;
	padding: 0 20rpx 20rpx;
}

.operation-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx;
	border-radius: 16rpx;
	font-size: 28rpx;
	font-weight: 500;
	
	&.batch-checkin {
		background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
		color: #ffffff;
	}
	
	&.batch-checkout {
		background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
		color: #ffffff;
	}
}

.btn-text {
	color: inherit;
	font-size: inherit;
}

/* 学生列表样式 */
.student-list {
	padding: 0 20rpx 40rpx;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.student-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	border-left: 6rpx solid #e9ecef;
	
	&.card-custody {
		border-left-color: #4CAF50;
	}
	
	&.card-leave {
		border-left-color: #FF9800;
	}
	
	&.card-not-custody {
		border-left-color: #f44336;
	}
}

.student-info {
	display: flex;
	align-items: flex-start;
	gap: 24rpx;
	margin-bottom: 20rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
}

.student-details {
	flex: 1;
}

.student-name-row {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
	font-weight: 500;
	
	&.status-custody {
		background: rgba(76, 175, 80, 0.1);
		color: #4CAF50;
	}
	
	&.status-leave {
		background: rgba(255, 152, 0, 0.1);
		color: #FF9800;
	}
	
	&.status-not-custody {
		background: rgba(244, 67, 54, 0.1);
		color: #f44336;
	}
}

.student-class {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.custody-time, .leave-time {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 8rpx;
}

.time-label {
	font-size: 24rpx;
	color: #666;
}

.time-value {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

.student-actions {
	display: flex;
	gap: 16rpx;
	justify-content: flex-end;
}

.action-btn {
	padding: 16rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	text-align: center;
	
	&.custody-checkin {
		background: rgba(76, 175, 80, 0.1);
		color: #4CAF50;
		border: 2rpx solid #4CAF50;
	}
	
	&.leave {
		background: rgba(255, 152, 0, 0.1);
		color: #FF9800;
		border: 2rpx solid #FF9800;
	}
	
	&.detail {
		background: rgba(102, 126, 234, 0.1);
		color: #667eea;
		border: 2rpx solid #667eea;
	}
}

/* 弹窗样式 - 简化版 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>