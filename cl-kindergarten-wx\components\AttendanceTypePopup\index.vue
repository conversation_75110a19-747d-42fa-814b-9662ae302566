<template>
	<u-popup 
		v-model="visible" 
		mode="center" 
		border-radius="24"
		:mask-close-able="true"
		@close="handleCancel"
	>
		<view class="popup-content">
			<view class="popup-header">
				<view class="header-icon">🎯</view>
				<text class="popup-title">选择考勤管理类型</text>
				<text class="popup-subtitle">请选择要进入的考勤管理模式</text>
			</view>
			
			<view class="popup-body">
				<view class="button-group">
					<view class="attendance-button student-btn" @click="handleStudentAttendance">
						<view class="btn-icon-wrapper">
							<view class="btn-icon">👨‍🎓</view>
						</view>
						<view class="btn-content">
							<text class="btn-title">学生考勤</text>
							<text class="btn-desc">管理学生日常签到考勤</text>
						</view>
						<view class="btn-arrow">›</view>
					</view>
					
					<view class="attendance-button custody-btn" @click="handleCustodyAttendance">
						<view class="btn-icon-wrapper">
							<view class="btn-icon">🏢</view>
						</view>
						<view class="btn-content">
							<text class="btn-title">托管考勤</text>
							<text class="btn-desc">管理学生托管时间考勤</text>
						</view>
						<view class="btn-arrow">›</view>
					</view>
				</view>
			</view>
			
			<view class="popup-footer">
				<view class="cancel-btn" @click="handleCancel">
					<text class="cancel-text">取消</text>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'AttendanceTypePopup',
	data() {
		return {
			visible: false
		}
	},
	methods: {
		show() {
			this.visible = true;
		},
		hide() {
			this.visible = false;
		},
		handleStudentAttendance() {
			this.$emit('select', 'student');
			this.hide();
		},
		handleCustodyAttendance() {
			this.$emit('select', 'custody');
			this.hide();
		},
		handleCancel() {
			this.$emit('cancel');
			this.hide();
		}
	}
}
</script>

<style lang="scss" scoped>
.popup-content {
	width: 680rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.popup-header {
	padding: 50rpx 40rpx 30rpx;
	text-align: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		bottom: -20rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 20rpx solid transparent;
		border-right: 20rpx solid transparent;
		border-top: 20rpx solid #764ba2;
	}
}

.header-icon {
	font-size: 60rpx;
	margin-bottom: 16rpx;
	display: block;
}

.popup-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 12rpx;
	display: block;
}

.popup-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
	font-weight: 400;
}

.popup-body {
	padding: 60rpx 40rpx 40rpx;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.attendance-button {
	display: flex;
	align-items: center;
	padding: 32rpx 28rpx;
	background: #ffffff;
	border-radius: 20rpx;
	border: 3rpx solid #f0f2f7;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
		transition: left 0.5s;
	}
	
	&:active {
		transform: scale(0.98);
		
		&::before {
			left: 100%;
		}
	}
}

.student-btn {
	border-color: #e3f2fd;
	box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.15);
	
	&:active {
		border-color: #2196F3;
		box-shadow: 0 12rpx 35rpx rgba(33, 150, 243, 0.25);
	}
}

.custody-btn {
	border-color: #e8f5e8;
	box-shadow: 0 8rpx 25rpx rgba(76, 175, 80, 0.15);
	
	&:active {
		border-color: #4CAF50;
		box-shadow: 0 12rpx 35rpx rgba(76, 175, 80, 0.25);
	}
}

.btn-icon-wrapper {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.student-btn .btn-icon-wrapper {
	background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
	box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.3);
}

.custody-btn .btn-icon-wrapper {
	background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
	box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
}

.btn-icon {
	font-size: 42rpx;
	filter: brightness(1.1);
}

.btn-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8rpx;
}

.btn-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	line-height: 1.2;
}

.btn-desc {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.3;
}

.btn-arrow {
	font-size: 40rpx;
	color: #cccccc;
	font-weight: 300;
	margin-left: 16rpx;
	flex-shrink: 0;
}

.student-btn:active .btn-arrow {
	color: #2196F3;
}

.custody-btn:active .btn-arrow {
	color: #4CAF50;
}

.popup-footer {
	padding: 0 40rpx 40rpx;
	text-align: center;
}

.cancel-btn {
	padding: 24rpx 60rpx;
	background: #f8f9fa;
	border-radius: 50rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
	display: inline-block;
	
	&:active {
		background: #e9ecef;
		transform: scale(0.96);
	}
}

.cancel-text {
	font-size: 28rpx;
	color: #6c757d;
	font-weight: 500;
}

/* 动画效果 */
@keyframes slideIn {
	from {
		transform: translateY(20rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.popup-content {
	animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 按钮悬浮效果 */
.attendance-button {
	&:active {
		.btn-icon-wrapper {
			transform: scale(1.1);
		}
		
		.btn-title {
			color: #333333;
		}
	}
}

.student-btn:active {
	.btn-title {
		color: #2196F3;
	}
}

.custody-btn:active {
	.btn-title {
		color: #4CAF50;
	}
}
</style>